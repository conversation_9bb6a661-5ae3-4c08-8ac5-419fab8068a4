import { Service } from "../models/service.entity.js";
import { SuperapiService } from "./superapi-service.js";
import { ResourceWithOptions } from "adminjs";
import { Components } from '../component-loader.js';
const superapiService = new SuperapiService({
  PRODUCT_NAME: 'flash-dev',
  SUPERAPI_HOST: 'https://api.droplet.today',
});

export const ServiceResource: ResourceWithOptions = {
    resource: Service,
    options: {
      properties: {
        id: {
          isVisible: { list: true, filter: true, show: true, edit: false },
          type: 'number'
        },
        userid: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'reference',
          reference: 'User'
        },
        packageid: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'reference',
          reference: 'Product'
        },
        billingcycle: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'string'
        },
        regdate: {
          isVisible: { list: true, filter: true, show: true, edit: false },
          type: 'date'
        },
        nextduedate: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'datetime'
        },
        domainstatus: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'string',
          availableValues: [
            { value: 'Pending', label: 'Pending' },
            { value: 'Active', label: 'Active' },
            { value: 'Suspended', label: 'Suspended' },
            { value: 'Terminated', label: 'Terminated' },
            { value: 'Cancelled', label: 'Cancelled' },
            { value: 'Fraud', label: 'Fraud' }
          ]
        },
        firstpaymentamount: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'number'
        },
        amount: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'number'
        },
        username: {
          isVisible: { list: false, filter: false, show: true, edit: true },
          type: 'string'
        },
        password: {
          isVisible: { list: false, filter: false, show: false, edit: true },
          type: 'password'
        },
        notes: {
          isVisible: { list: false, filter: false, show: true, edit: true },
          type: 'textarea'
        }
      },
      actions: {

        edit: {
          after: async (response, request, context) => {
            const updatedRecord = response.record;
            if (request.payload.packageid) {
              await superapiService.syncServicePackage(updatedRecord.params.id, +request.payload.packageid);
            } 
            
            if (request.payload.nextduedate) {
              await superapiService.syncServiceExpireDate(updatedRecord.params.id, request.payload.nextduedate);
            }
            return response;
          },
        },
        superapi: {
          actionType: 'record',
          handler: async (request, response, context) => {
            const serviceId = request.query.serviceId;
            const service = await superapiService.fetchService({ serviceId: serviceId });
            return service;
          },
        },
      },
    },
  };