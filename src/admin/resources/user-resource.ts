import { User } from "../models/user.entity.js";
import bcrypt from 'bcrypt';

export const UserResource = {
  resource: User,
  options: {
    properties: {
      id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number'
      },
      email: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      firstname: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      lastname: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      emailVerified: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      },
      status: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string',
        availableValues: [
          { value: 'Active', label: 'Active' },
          { value: 'Inactive', label: 'Inactive' },
          { value: 'Closed', label: 'Closed' }
        ]
      },
      datecreated: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'date'
      },
      createdAt: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'date'
      },
      updatedAt: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'date'
      },
      password: {
        isVisible: { list: false, filter: false, show: false, edit: false },
        type: 'string'
      },
      cardLastFour: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        custom: {
          component: (v) => formatBufferOrValue(v)
        }
      },
      gatewayId: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string'
      },
      lastLogin: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string'
      },
      ip: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string'
      },
      host: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string'
      },
      pwResetKey: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'string'
      },
      pwResetExpiry: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'date'
      },
      emailOptOut: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      },
      overrideAutoClose: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      },
      allowSso: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      },
      emailPreferences: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      billingCid: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number'
      },
      expDate: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        custom: {
          component: (v) => formatBufferOrValue(v),
        }
      },
      startDate: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        custom: {
          component:(v) => formatBufferOrValue(v),
        }
      },
      cardNum: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        custom: {
          component: (v) => formatBufferOrValue(v)
        }
      },
      issueNumber: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        custom: {
          component: (v) => formatBufferOrValue(v)
        }
      },
      bankCode: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        custom: {
          component: (v) => formatBufferOrValue(v)
        }
      },
      bankAcct: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        custom: {
          component: (v) => formatBufferOrValue(v)
        }
      },
      bankType: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string',
        custom: {
          component: (v) => formatBufferOrValue(v)
        }
      }
    },
    actions: {
      'update-password': {
        actionType: 'record',
        handler: async (request, response, context) => {
          const { userId, password } = request.payload;
          
          if (!password || password.length < 8) {
            throw new Error('Password must be at least 8 characters long');
          }

          const user = await context.resource.findOne(userId);
          if (!user) {
            throw new Error('User not found');
          }

          // Hash password
          const salt = await bcrypt.genSalt(10);
          const hashedPassword = await bcrypt.hash(password, salt);

          // Update user password
          await context.resource.update(userId, {
            password: hashedPassword
          });

          return {
            notice: {
              message: 'Password successfully updated',
              type: 'success'
            }
          };
        },
      }
    }
  }
};


// 辅助函数来正确处理和显示 Buffer 类型数据
function formatBufferOrValue(value) {
  const formatValue = (val) => {
    if (!val) return '-';
    
    // 处理 Buffer 对象
    if (val && typeof val === 'object' && val.type === 'Buffer') {
      // 如果 Buffer 数组为空，返回一个占位符
      if (!val.data || val.data.length === 0) {
        return '-';
      }
      // 尝试将 Buffer 转换为字符串
      try {
        return Buffer.from(val.data).toString();
      } catch (e) {
        return '-';
      }
    }
    
    // 处理其他类型
    try {
      return typeof val === 'object' ? formatValue(val) : String(val);
    } catch (e) {
      return '-';
    }
  };

  // 返回格式化后的字符串，而不是直接返回对象
  return formatValue(value);
}