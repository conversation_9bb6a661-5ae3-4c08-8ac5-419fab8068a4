import {
  BaseEntity,
  Column, 
  <PERSON>ti<PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  OneToMany,
  JoinC<PERSON>umn,
  OneToOne
} from 'typeorm';
import * as moment from 'moment';
export enum InvoiceStatus {
  Unpaid = 'Unpaid',
  Paid = 'Paid',
  Cancelled = 'Cancelled',
}

@Entity('tblinvoices')
export class Invoice extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({type: 'int', nullable: false})
  userid: number;

  @Column({type: 'date', nullable: false})
  date: Date;

  @Column({type: 'date', nullable: false})
  duedate: Date;

  @Column({type: 'date', nullable: false})
  datepaid: Date;

  @Column({type: 'text', nullable: false})
  status: string;

  @Column({type: 'text', nullable: false})
  paymentmethod: string;

  @Column({type: 'decimal', default: 0.00, precision: 10, scale: 2, nullable: false})
  total: number;

  @Column({type: 'decimal', default: 0.00, precision: 10, scale: 2, nullable: false})
  subtotal?: number;

  @Column({type: 'text', nullable: false})
  invoicenum: string;

  @Column({type: 'decimal', default: 0.00, precision: 10, scale: 2, nullable: false})
  credit: number;

  @Column({type: 'decimal', default: 0.00, precision: 10, scale: 2, nullable: false})
  tax: number;

  @Column({type: 'decimal', default: 0.00, precision: 10, scale: 2, nullable: false})
  tax2: number;

  @Column({type: 'decimal', default: 0.00, precision: 10, scale: 2, nullable: false})
  taxrate: number;

  @Column({type: 'decimal', default: 0.00, precision: 10, scale: 2, nullable: false})
  taxrate2: number;

  @Column({type: 'text', nullable: false})
  notes?: string;

  @Column({ name: 'created_at', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ name: 'updated_at', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @OneToMany(() => InvoiceItem, (item) => item.invoice)
  items: InvoiceItem[];

  description?: string;
  serviceId?: number;
  serviceStatus?: string;
  nextDueDate?: string;
  billingcycle?: string;

  toJson() {
    return {
      id: this.id,
      userid : this.userid,
      date: this.date,
      duedate: this.duedate,
      nextDueDate: this.nextDueDate,
      datepaid: this.datepaid,
      status: this.status,
      total: this.total,
      description: this.description,
      serviceId: this.serviceId,
      serviceStatus: this.serviceStatus,
      paymentmethod: this.paymentmethod,
      billingcycle: this.billingcycle,
      subTotal: this.subtotal,
      notes: this.notes,
    };
  }

  private static dateMatcher(description: string, isStart: boolean = true) {
    let matcher;
    if (isStart) {
      matcher = description.match(/\d{2,4}[-/]\d{2}[-/]\d{2,4}/);
    } else {
      matcher = description.match(/- \d{2,4}[-/]\d{2}[-/]\d{2,4}/);
    }
    if (matcher) {
      const matchedDate = isStart ? matcher.pop() : matcher.pop().substring(2);
      return this.dateFormat(matchedDate);
    }
    return '';
  }

  private static dateFormat(date) {
    if (date.match('/')) {
      date = date.split('/').reverse().join('');
    }
    return moment.utc(date).format('YYYY-MM-DD HH:mm:ss');
  }

  static fromData(data: any): Invoice {
    const {
      description,
      duedate,
      status,
      total,
      userid,
      serviceId,
      serviceStatus,
      id,
      date,
      nextDueDate,
      datepaid,
      billingcycle,
      subtotal,
      paymentmethod,
      notes,
    } = data;
    const invoice = new Invoice();
    invoice.id = id;
    invoice.duedate = duedate;
    invoice.status = status;
    invoice.total = total;
    invoice.userid = userid;
    invoice.description = description;
    invoice.serviceId = serviceId || 0;
    invoice.serviceStatus = serviceStatus;
    invoice.billingcycle = billingcycle;
    invoice.nextDueDate = nextDueDate;
    invoice.subtotal = subtotal;
    invoice.date = date;
    invoice.notes = notes;
    if (invoice.status === 'Paid') {
      invoice.datepaid = datepaid;
      invoice.paymentmethod = paymentmethod;
    }
    return invoice;
  }

  daysTillDue(): number {
    return moment.duration(moment.utc(this.duedate).diff(moment.utc())).asDays();
  }

  isPayed(): boolean {
    return this.status === 'Paid';
  }
  // duedate only has date, so for newly created invoice, due day is always minus
  isDue(): boolean {
    return this.daysTillDue() < -1;
  }
}


export enum InvoiceItemType {
  Hosting = 'Hosting',
  Upgrade = 'Upgrade',
  Flow = 'Flow',
  Balance = 'Balance',
  Trial = 'Trial',
  Other = 'Other',
}

@Entity('tblinvoiceitems')
export class InvoiceItem extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({type: 'int', nullable: false})
  invoiceid: number;

  @Column({type: 'int', nullable: false})
  amount: number;

  @Column({type: 'int', nullable: false})
  userid: number;

  @Column({type: 'int', nullable: false})
  relid: number;

  @Column({type: 'varchar', nullable: false})
  type: string;

  @Column({type: 'varchar', nullable: false})
  description: string;

  @Column({type: 'date', nullable: false})
  duedate: Date;

  @Column({type: 'varchar', nullable: false})
  paymentmethod: string;

  @Column({type: 'int', nullable: false})
  taxed: number;

  @Column({type: 'text', nullable: false})
  notes?: string;

  @OneToOne(() => Invoice, (invoice) => invoice.items)
  @JoinColumn({name: 'invoiceid'})
  invoice: Invoice;

  toJson() {
    return {
      id: this.id,
      invoiceid : this.invoiceid,
      amount: this.amount,
      userid : this.userid,
      relid : this.relid,
      type: this.type,
      description: this.description,
      duedate: this.duedate,
    };
  }
}
